import React from 'react';
import ReactDOM from 'react-dom/client';
import { useRequest } from 'ahooks';
import init, { chushihuawangguan, get_qingqiu } from './qudong/wangluoqingqiu/wangluoqingqiu.js';
import {
  tiqu_wangzhan_jichuxinxi,
  cunchu_dao_huancun,
  cong_huancun_duqu
} from './gongju/wangzhanjichuxinxi_huancun.js';
import {
  yingyong_wangzhan_jichuxinxi,
  kuaisu_yingyong_mingcheng_tubiao
} from './gongju/wangzhan_dongtai_yingyong.js';
// 导入明暗布局组件
import {
  minganbuju,
  Zhutitiqigong,
  useShiyongzhutiqiehuan,
  donghualeixin,
  donghuasudu,
  zhutileixing,
} from './zujian/minganbuju';

const yuanshiconsole = console.log;
console.log = function(...args) {
  const message = args.join(' ');
  if (message.includes('[AgentProvider]') ||
      message.includes('[PanelsProvider]') ||
      message.includes('[PanelWrapper]') ||
      message.includes('[stagewise]')) {
    return;
  }
  yuanshiconsole.apply(console, args);
};

let chushipromise = null;
let yichushihua = false;
let wasm_yichushihua = false;

async function chushihua_wasm() {
    if (wasm_yichushihua) {
        return;
    }
    await init();
    chushihuawangguan('http://127.0.0.1:8098');
    wasm_yichushihua = true;
}

async function qianduanchushihua() {
  if (yichushihua) {
    return null;
  }
  if (chushipromise) {
    return await chushipromise;
  }

  chushipromise = (async () => {
    try {
      await chushihua_wasm();

      let wangzhan_xinxi = cong_huancun_duqu();

      if (wangzhan_xinxi) {
        kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
        yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);
        yichushihua = true;
        return {
          laiyuan: 'huancun',
          shuju: wangzhan_xinxi
        };
      }

      const xiangying = await get_qingqiu(
        '/jiekou/wangzhanjichuxinxi',
        null,
        false,
        false,
        5000,
        1
      );

      wangzhan_xinxi = tiqu_wangzhan_jichuxinxi(xiangying);
      if (!wangzhan_xinxi) {
        yichushihua = true;
        throw new Error('提取网站基础信息失败');
      }

      kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi);
      cunchu_dao_huancun(wangzhan_xinxi);
      const yingyong_jieguo = yingyong_wangzhan_jichuxinxi(wangzhan_xinxi);

      yichushihua = true;
      return {
        laiyuan: 'wangluo',
        shuju: wangzhan_xinxi,
        yingyong_jieguo: yingyong_jieguo
      };

    } catch (cuowu) {
      console.error('前端初始化失败:', cuowu);
      chushipromise = null;
      throw cuowu;
    }
  })();

  return await chushipromise;
}

// 主题切换测试组件
function Zhutiqiehuanceshi() {
  const {
    qiehuandaoxiayigezhuti,
    dangqianzhuti,
    shifoianheizuti,
    qiehuanzhuti,
    zhutileixing
  } = useShiyongzhutiqiehuan();

  return (
    <div style={{
      padding: '20px',
      marginBottom: '20px',
      backgroundColor: shifoianheizuti ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      borderRadius: '8px',
      border: `1px solid ${shifoianheizuti ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'}`
    }}>
      <h3 style={{ margin: '0 0 10px 0', fontSize: '18px', fontWeight: 'bold' }}>
        🌓 明暗主题测试
      </h3>
      <p style={{ margin: '0 0 15px 0', opacity: 0.8 }}>
        当前主题: {dangqianzhuti} ({shifoianheizuti ? '🌙 暗黑模式' : '☀️ 明亮模式'})
      </p>

      <div style={{ display: 'flex', gap: '10px', marginTop: '15px', flexWrap: 'wrap' }}>
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
          onClick={qiehuandaoxiayigezhuti}
        >
          🔄 切换主题
        </button>
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: 'transparent',
            color: 'currentColor',
            border: '1px solid currentColor',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
          onClick={() => qiehuanzhuti(zhutileixing.MINGLIANG)}
        >
          ☀️ 明亮
        </button>
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: 'transparent',
            color: 'currentColor',
            border: '1px solid currentColor',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
          onClick={() => qiehuanzhuti(zhutileixing.ANHEI)}
        >
          🌙 暗黑
        </button>
      </div>

      <p style={{ margin: '10px 0 0 0', fontSize: '12px', opacity: 0.6 }}>
        💡 提示: 使用 Ctrl+Shift+T 快捷键快速切换主题
      </p>
    </div>
  );
}


function App() {
  // 使用useRequest管理前端初始化
  const { loading, error } = useRequest(qianduanchushihua, {
    manual: false, // 自动执行
    cacheKey: 'wasm-init', // 缓存key，防止重复初始化
    staleTime: Infinity, // 永不过期，确保只初始化一次
  });

  return (
    <minganbuju
      donghualeixin={donghualeixin.HUADONG}
      donghuasudu={donghuasudu.ZHONGDENG}
    >
      <div style={{ padding: '20px', minHeight: '100vh' }}>
        <h1>前端应用测试</h1>

        {/* 主题切换控件 */}
        <Zhutiqiehuanceshi />

        {/* 初始化状态显示 */}
        <div style={{
          padding: '20px',
          marginBottom: '20px',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '8px',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <h2>初始化状态</h2>

          {loading && (
            <p style={{ color: '#17a2b8' }}>
              ⏳ 正在初始化WASM模块...
            </p>
          )}

          {error && (
            <p style={{ color: '#dc3545' }}>
              ❌ 初始化失败: {error.message}
            </p>
          )}

          {!loading && !error && (
            <p style={{ color: '#28a745' }}>
              ✅ 初始化成功！请查看控制台输出
            </p>
          )}
        </div>

        {/* 功能列表 */}
        <div style={{
          padding: '20px',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '8px',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <h2>功能列表</h2>
          <ul>
            <li>🎨 自动主题切换和持久化存储</li>
            <li>✨ 基于 framer-motion 的流畅动画</li>
            <li>🎯 styled-components 主题系统</li>
            <li>⌨️ 键盘快捷键支持</li>
            <li>📱 响应式设计</li>
          </ul>
        </div>
      </div>
    </minganbuju>
  );
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App />);
