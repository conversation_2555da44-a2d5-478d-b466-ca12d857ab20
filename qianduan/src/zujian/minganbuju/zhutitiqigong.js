import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeProvider } from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocalStorageState } from 'ahooks';
import {
  zhutileixing,
  zhutiyingshe,
  morenzuti
} from './zhutipeizhi.js';

// 创建主题上下文
const zhutishangxiaweng = createContext();

// 本地存储键名
const ZHUTI_CUNCHU_JIAN = 'minganbuju_zhuti';

// 主题提供器组件
export const Zhutitiqigong = ({ children, chushipzhuti = zhutileixing.MINGLIANG }) => {
  // 使用 ahooks 的 useLocalStorageState 管理主题持久化
  const [dangqianzhuti, shezhi_dangqianzhuti] = useLocalStorageState(
    ZHUTI_CUNCHU_JIAN,
    {
      defaultValue: chushipzhuti,
    }
  );

  // 动画状态
  const [donghuazhuangtai, shezhi_donghuazhuangtai] = useState(false);

  // 获取当前主题对象
  const zhutidixiang = zhutiyingshe[dangqianzhuti] || morenzuti;

  // 切换主题函数
  const qiehuanzhuti = (xinzhuti) => {
    if (xinzhuti && xinzhuti !== dangqianzhuti) {
      shezhi_donghuazhuangtai(true);
      
      // 延迟切换主题，让动画先开始
      setTimeout(() => {
        shezhi_dangqianzhuti(xinzhuti);
        
        // 动画完成后重置状态
        setTimeout(() => {
          shezhi_donghuazhuangtai(false);
        }, 300);
      }, 150);
    }
  };

  // 切换到下一个主题
  const qiehuandaoxiayigezhuti = () => {
    const zhutiliebiao = Object.values(zhutileixing);
    const dangqiansuoyin = zhutiliebiao.indexOf(dangqianzhuti);
    const xiayigesuoyin = (dangqiansuoyin + 1) % zhutiliebiao.length;
    qiehuanzhuti(zhutiliebiao[xiayigesuoyin]);
  };

  // 检查是否为暗黑主题
  const shifoianheizuti = dangqianzhuti === zhutileixing.ANHEI;

  // 上下文值
  const shangxiawengzhi = {
    dangqianzhuti,
    zhutidixiang,
    qiehuanzhuti,
    qiehuandaoxiayigezhuti,
    shifoianheizuti,
    donghuazhuangtai,
    zhutileixing,
  };

  // 动画配置
  const donghuapeizhi = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: {
      duration: 0.3,
      ease: 'easeInOut',
    },
  };

  return (
    <zhutishangxiaweng.Provider value={shangxiawengzhi}>
      <ThemeProvider theme={zhutidixiang}>
        <AnimatePresence mode="wait">
          <motion.div
            key={dangqianzhuti}
            {...donghuapeizhi}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: zhutidixiang.yanse.beijing,
              color: zhutidixiang.yanse.wenzi_zhuyao,
              transition: `all ${zhutidixiang.donghua.sujian.zhongdeng} ${zhutidixiang.donghua.huanman.biaozhun}`,
            }}
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </ThemeProvider>
    </zhutishangxiaweng.Provider>
  );
};

// 自定义 Hook 用于使用主题上下文
export const useShiyongzhuti = () => {
  const shangxiaweng = useContext(zhutishangxiaweng);
  
  if (!shangxiaweng) {
    throw new Error('useShiyongzhuti 必须在 Zhutitiqigong 内部使用');
  }
  
  return shangxiaweng;
};

// 高阶组件：为组件提供主题功能
export const daizhutizujian = (Zujian) => {
  return React.forwardRef((props, ref) => (
    <Zhutitiqigong>
      <Zujian {...props} ref={ref} />
    </Zhutitiqigong>
  ));
};

// 主题切换 Hook
export const useShiyongzhutiqiehuan = () => {
  const {
    qiehuanzhuti,
    qiehuandaoxiayigezhuti,
    dangqianzhuti,
    shifoianheizuti
  } = useShiyongzhuti();

  return {
    qiehuanzhuti,
    qiehuandaoxiayigezhuti,
    dangqianzhuti,
    shifoianheizuti,
    zhutileixing, // 添加主题类型常量
  };
};
